// Vue.js 应用
new Vue({
    el: '#app',
    data() {
        return {
            candidates: [
                { name: '沈俊', votes: 0 },
                { name: '冯飞', votes: 0 },
                { name: '杨卫平', votes: 0 },
                { name: '傅云峰', votes: 0 },
                { name: '栾琛', votes: 0 },
                { name: '崔萌萌', votes: 0 }
            ],
            backupPersonnel: [
                { name: '徐海涛', votes: 0 },
                { name: '姜泽', votes: 0 },
                { name: '李毅', votes: 0 },
                { name: '曲兵', votes: 0 },
                { name: '韩吉德', votes: 0 },
                { name: '孙满新', votes: 0 },
                { name: '伊燕', votes: 0 },
                { name: '徐麟', votes: 0 },
                { name: '张科首', votes: 0 },
                { name: '郝长平', votes: 0 },
                { name: '崔晓霞', votes: 0 },
                { name: '张军凯', votes: 0 },
                { name: '姜峰', votes: 0 },
                { name: '段文明', votes: 0 }
            ]
        }
    },
    computed: {
        // 合并所有人员
        allPeople() {
            return [...this.candidates, ...this.backupPersonnel];
        },
        // 计算总票数
        totalVotes() {
            return this.allPeople.reduce((total, person) => total + person.votes, 0);
        },
        // 计算候选人票数
        candidateVotes() {
            return this.candidates.reduce((total, person) => total + person.votes, 0);
        },
        // 计算备用人员票数
        backupVotes() {
            return this.backupPersonnel.reduce((total, person) => total + person.votes, 0);
        }
    },
    methods: {
        // 增加票数
        increaseVote(index, type) {
            const person = type === 'candidate' ? this.candidates[index] : this.backupPersonnel[index];
            person.votes++;
            this.saveData();
            this.showMessage('success', `${person.name} 票数 +1`);
        },

        // 减少票数
        decreaseVote(index, type) {
            const person = type === 'candidate' ? this.candidates[index] : this.backupPersonnel[index];
            if (person.votes > 0) {
                person.votes--;
                this.saveData();
                this.showMessage('warning', `${person.name} 票数 -1`);
            }
        },

        // 保存数据到本地存储
        saveData() {
            try {
                const dataToSave = {
                    candidates: this.candidates,
                    backupPersonnel: this.backupPersonnel
                };
                localStorage.setItem('votingData', JSON.stringify(dataToSave));
                console.log('数据已保存到本地存储');
            } catch (error) {
                console.error('保存数据失败:', error);
                this.showMessage('error', '保存数据失败');
            }
        },

        // 从本地存储加载数据
        loadData() {
            try {
                const savedData = localStorage.getItem('votingData');
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    // 检查新格式数据
                    if (parsedData.candidates && parsedData.backupPersonnel) {
                        // 加载候选人数据
                        if (Array.isArray(parsedData.candidates) && parsedData.candidates.length === this.candidates.length) {
                            parsedData.candidates.forEach((person, index) => {
                                if (person.name && typeof person.votes === 'number') {
                                    this.candidates[index].votes = Math.max(0, person.votes);
                                }
                            });
                        }
                        // 加载备用人员数据
                        if (Array.isArray(parsedData.backupPersonnel) && parsedData.backupPersonnel.length === this.backupPersonnel.length) {
                            parsedData.backupPersonnel.forEach((person, index) => {
                                if (person.name && typeof person.votes === 'number') {
                                    this.backupPersonnel[index].votes = Math.max(0, person.votes);
                                }
                            });
                        }
                        console.log('数据已从本地存储加载');
                        this.showMessage('success', '数据加载成功');
                    }
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                this.showMessage('error', '加载数据失败');
            }
        },

        // 重置所有票数
        resetAllVotes() {
            this.$confirm('确定要重置所有人的票数吗？此操作不可撤销。', '确认重置', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.candidates.forEach(person => {
                    person.votes = 0;
                });
                this.backupPersonnel.forEach(person => {
                    person.votes = 0;
                });
                this.saveData();
                this.showMessage('success', '所有票数已重置');
            }).catch(() => {
                this.showMessage('info', '已取消重置操作');
            });
        },

        // 导出数据
        exportData() {
            try {
                const dataToExport = {
                    exportTime: new Date().toLocaleString('zh-CN'),
                    totalVotes: this.totalVotes,
                    candidateVotes: this.candidateVotes,
                    backupVotes: this.backupVotes,
                    candidates: this.candidates.map(person => ({
                        name: person.name,
                        votes: person.votes,
                        percentage: this.totalVotes > 0 ? ((person.votes / this.totalVotes) * 100).toFixed(2) + '%' : '0%'
                    })),
                    backupPersonnel: this.backupPersonnel.map(person => ({
                        name: person.name,
                        votes: person.votes,
                        percentage: this.totalVotes > 0 ? ((person.votes / this.totalVotes) * 100).toFixed(2) + '%' : '0%'
                    }))
                };

                const dataStr = JSON.stringify(dataToExport, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `投票统计_${new Date().toISOString().slice(0, 10)}.json`;
                link.click();

                this.showMessage('success', '数据导出成功');
            } catch (error) {
                console.error('导出数据失败:', error);
                this.showMessage('error', '导出数据失败');
            }
        },

        // 显示消息提示
        showMessage(type, message) {
            this.$message({
                type: type,
                message: message,
                duration: 2000,
                showClose: true
            });
        },

        // 格式化数字显示
        formatNumber(num) {
            return num.toLocaleString();
        }
    },

    // 组件挂载时加载数据
    mounted() {
        this.loadData();

        // 监听页面关闭前事件，确保数据保存
        window.addEventListener('beforeunload', () => {
            this.saveData();
        });

        // 定期自动保存（每30秒）
        setInterval(() => {
            this.saveData();
        }, 30000);

        console.log('投票统计系统已启动');
        this.showMessage('success', '投票统计系统已启动');
    },

    // 监听数据变化
    watch: {
        candidates: {
            handler() {
                // 当候选人数组发生变化时自动保存
                this.saveData();
            },
            deep: true
        },
        backupPersonnel: {
            handler() {
                // 当备用人员数组发生变化时自动保存
                this.saveData();
            },
            deep: true
        }
    }
});
