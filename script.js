// Vue.js 应用
new Vue({
    el: '#app',
    data() {
        return {
            people: [
                { name: '张三', votes: 0 },
                { name: '李四', votes: 0 },
                { name: '王五', votes: 0 },
                { name: '赵六', votes: 0 },
                { name: '钱七', votes: 0 },
                { name: '孙八', votes: 0 },
                { name: '周九', votes: 0 },
                { name: '吴十', votes: 0 },
                { name: '郑十一', votes: 0 },
                { name: '王十二', votes: 0 },
                { name: '冯十三', votes: 0 },
                { name: '陈十四', votes: 0 },
                { name: '褚十五', votes: 0 },
                { name: '卫十六', votes: 0 },
                { name: '蒋十七', votes: 0 },
                { name: '沈十八', votes: 0 },
                { name: '韩十九', votes: 0 },
                { name: '杨二十', votes: 0 },
                { name: '朱二十一', votes: 0 },
                { name: '秦二十二', votes: 0 }
            ]
        }
    },
    computed: {
        // 计算总票数
        totalVotes() {
            return this.people.reduce((total, person) => total + person.votes, 0);
        }
    },
    methods: {
        // 增加票数
        increaseVote(index) {
            this.people[index].votes++;
            this.saveData();
            this.showMessage('success', `${this.people[index].name} 票数 +1`);
        },
        
        // 减少票数
        decreaseVote(index) {
            if (this.people[index].votes > 0) {
                this.people[index].votes--;
                this.saveData();
                this.showMessage('warning', `${this.people[index].name} 票数 -1`);
            }
        },
        
        // 保存数据到本地存储
        saveData() {
            try {
                localStorage.setItem('votingData', JSON.stringify(this.people));
                console.log('数据已保存到本地存储');
            } catch (error) {
                console.error('保存数据失败:', error);
                this.showMessage('error', '保存数据失败');
            }
        },
        
        // 从本地存储加载数据
        loadData() {
            try {
                const savedData = localStorage.getItem('votingData');
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    // 确保数据结构正确且人数匹配
                    if (Array.isArray(parsedData) && parsedData.length === this.people.length) {
                        parsedData.forEach((person, index) => {
                            if (person.name && typeof person.votes === 'number') {
                                this.people[index].votes = Math.max(0, person.votes);
                            }
                        });
                        console.log('数据已从本地存储加载');
                        this.showMessage('success', '数据加载成功');
                    }
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                this.showMessage('error', '加载数据失败');
            }
        },
        
        // 重置所有票数
        resetAllVotes() {
            this.$confirm('确定要重置所有人的票数吗？此操作不可撤销。', '确认重置', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.people.forEach(person => {
                    person.votes = 0;
                });
                this.saveData();
                this.showMessage('success', '所有票数已重置');
            }).catch(() => {
                this.showMessage('info', '已取消重置操作');
            });
        },
        
        // 导出数据
        exportData() {
            try {
                const dataToExport = {
                    exportTime: new Date().toLocaleString('zh-CN'),
                    totalVotes: this.totalVotes,
                    people: this.people.map(person => ({
                        name: person.name,
                        votes: person.votes,
                        percentage: this.totalVotes > 0 ? ((person.votes / this.totalVotes) * 100).toFixed(2) + '%' : '0%'
                    }))
                };
                
                const dataStr = JSON.stringify(dataToExport, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `投票统计_${new Date().toISOString().slice(0, 10)}.json`;
                link.click();
                
                this.showMessage('success', '数据导出成功');
            } catch (error) {
                console.error('导出数据失败:', error);
                this.showMessage('error', '导出数据失败');
            }
        },
        
        // 显示消息提示
        showMessage(type, message) {
            this.$message({
                type: type,
                message: message,
                duration: 2000,
                showClose: true
            });
        },
        
        // 格式化数字显示
        formatNumber(num) {
            return num.toLocaleString();
        }
    },
    
    // 组件挂载时加载数据
    mounted() {
        this.loadData();
        
        // 监听页面关闭前事件，确保数据保存
        window.addEventListener('beforeunload', () => {
            this.saveData();
        });
        
        // 定期自动保存（每30秒）
        setInterval(() => {
            this.saveData();
        }, 30000);
        
        console.log('投票统计系统已启动');
        this.showMessage('success', '投票统计系统已启动');
    },
    
    // 监听数据变化
    watch: {
        people: {
            handler() {
                // 当people数组发生变化时自动保存
                this.saveData();
            },
            deep: true
        }
    }
});
