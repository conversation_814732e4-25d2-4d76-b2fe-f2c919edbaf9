/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    background: #f5f7fa;
    min-height: 100vh;
    color: #2c3e50;
    line-height: 1.6;
}

/* 主容器 */
.container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    background: #ffffff;
    padding: 25px 40px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;
}

.title {
    font-size: 2.2rem;
    color: #2c3e50;
    font-weight: 600;
    letter-spacing: 1px;
}

.title i {
    margin-right: 12px;
    font-size: 2.2rem;
    color: #5a6c7d;
}

.stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.stats-card {
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    min-width: 160px;
    background: #ffffff;
}

.stats-card.total {
    border-left: 4px solid #409eff;
}

.stats-card.candidate {
    border-left: 4px solid #e6a23c;
}

.stats-card.backup {
    border-left: 4px solid #67c23a;
}

.stats-card .el-card__body {
    padding: 18px 24px;
}

.stats-item {
    display: flex;
    align-items: center;
    color: #2c3e50;
}

.stats-label {
    font-size: 1rem;
    margin-right: 8px;
    color: #606266;
    font-weight: 500;
}

.stats-value {
    font-size: 1.6rem;
    font-weight: 600;
    color: #2c3e50;
}

/* 分组标题 */
.section-header {
    margin: 35px 0 25px 0;
    padding-left: 0;
}

.section-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2c3e50;
    background: #ffffff;
    padding: 16px 24px;
    border-radius: 6px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;
    letter-spacing: 0.5px;
}

.candidate-header .section-title {
    border-left: 4px solid #e6a23c;
    color: #2c3e50;
}

.backup-header .section-title {
    border-left: 4px solid #67c23a;
    color: #2c3e50;
}

.section-title i {
    margin-right: 8px;
    font-size: 1.2rem;
    color: #909399;
}

/* 投票网格 */
.voting-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 人员卡片 */
.person-card {
    background: #ffffff;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: 1px solid #dcdfe6;
    overflow: hidden;
}

.person-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    border-color: #c0c4cc;
}

/* 候选人卡片特殊样式 */
.candidate-card {
    border-left: 4px solid #e6a23c;
    position: relative;
}

.candidate-card::before {
    content: '重点';
    position: absolute;
    top: 12px;
    right: 16px;
    background: #e6a23c;
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
}

.candidate-card:hover {
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.2);
    border-left-color: #d19e2b;
}

/* 备用人员卡片样式 */
.backup-card {
    border-left: 4px solid #67c23a;
}

.backup-card:hover {
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.15);
    border-left-color: #5daf34;
}

.person-card .el-card__body {
    padding: 25px;
}

.person-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.person-avatar {
    width: 50px;
    height: 50px;
    background: #909399;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.4rem;
    box-shadow: none;
}

.candidate-avatar {
    background: #e6a23c;
}

.backup-avatar {
    background: #67c23a;
}

.person-details {
    flex: 1;
}

.person-name {
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.vote-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.vote-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.vote-label {
    font-size: 0.9rem;
    color: #606266;
    font-weight: 500;
}

.vote-count {
    font-size: 1.3rem;
    font-weight: 600;
    color: #409eff;
}

.vote-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.vote-input {
    width: 100px;
}

.vote-input .el-input__inner {
    text-align: center;
    font-weight: bold;
    font-size: 1.1rem;
}

/* 底部按钮 */
.footer {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 24px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e8eaec;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.footer .el-button {
    padding: 10px 20px;
    font-size: 0.9rem;
    border-radius: 4px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .voting-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 15px;
    }

    .title {
        font-size: 2rem;
    }

    .container {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .voting-grid {
        grid-template-columns: 1fr;
    }

    .person-info {
        flex-direction: column;
        text-align: center;
    }

    .vote-controls {
        justify-content: center;
    }

    .footer {
        flex-direction: column;
        align-items: center;
    }
}

/* Element UI 自定义样式 */
.el-button--primary {
    background: #409eff;
    border-color: #409eff;
}

.el-button--primary:hover {
    background: #66b1ff;
    border-color: #66b1ff;
}

.el-button--danger {
    background: #f56c6c;
    border-color: #f56c6c;
}

.el-button--danger:hover {
    background: #f78989;
    border-color: #f78989;
}

.el-button--warning {
    background: #e6a23c;
    border-color: #e6a23c;
}

.el-button--warning:hover {
    background: #ebb563;
    border-color: #ebb563;
}

.el-button--success {
    background: #67c23a;
    border-color: #67c23a;
}

.el-button--success:hover {
    background: #85ce61;
    border-color: #85ce61;
}

.el-input-number .el-input__inner {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
}

.el-input-number .el-input__inner:focus {
    border-color: #409eff;
}
