/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    min-height: 100vh;
    color: #333;
}

/* 主容器 */
.container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px 30px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.title {
    font-size: 2.5rem;
    color: #409EFF;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.title i {
    margin-right: 15px;
    font-size: 2.8rem;
}

.stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.stats-card {
    border: none;
    border-radius: 12px;
    min-width: 150px;
}

.stats-card.total {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.candidate {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.backup {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-card .el-card__body {
    padding: 15px 25px;
}

.stats-item {
    display: flex;
    align-items: center;
    color: white;
}

.stats-label {
    font-size: 1.1rem;
    margin-right: 10px;
}

.stats-value {
    font-size: 1.8rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 分组标题 */
.section-header {
    margin: 30px 0 20px 0;
    text-align: center;
}

.section-title {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 30px;
    border-radius: 25px;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.candidate-header .section-title {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.backup-header .section-title {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.section-title i {
    margin-right: 10px;
    font-size: 1.5rem;
}

/* 投票网格 */
.voting-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 人员卡片 */
.person-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.person-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* 候选人卡片特殊样式 */
.candidate-card {
    border: 3px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #f093fb 0%, #f5576c 100%) border-box;
    position: relative;
}

.candidate-card::before {
    content: '★';
    position: absolute;
    top: 10px;
    right: 15px;
    color: #f5576c;
    font-size: 1.2rem;
    font-weight: bold;
}

.candidate-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(245, 87, 108, 0.3);
}

/* 备用人员卡片样式 */
.backup-card {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) border-box;
}

.person-card .el-card__body {
    padding: 25px;
}

.person-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.person-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.candidate-avatar {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 4px 15px rgba(245, 87, 108, 0.4);
}

.backup-avatar {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

.person-details {
    flex: 1;
}

.person-name {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 600;
}

.vote-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.vote-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.vote-label {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

.vote-count {
    font-size: 1.5rem;
    font-weight: bold;
    color: #409EFF;
    background: linear-gradient(135deg, #409EFF, #67C23A);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.vote-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.vote-input {
    width: 100px;
}

.vote-input .el-input__inner {
    text-align: center;
    font-weight: bold;
    font-size: 1.1rem;
}

/* 底部按钮 */
.footer {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.footer .el-button {
    padding: 12px 25px;
    font-size: 1rem;
    border-radius: 8px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .voting-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 15px;
    }

    .title {
        font-size: 2rem;
    }

    .container {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .voting-grid {
        grid-template-columns: 1fr;
    }

    .person-info {
        flex-direction: column;
        text-align: center;
    }

    .vote-controls {
        justify-content: center;
    }

    .footer {
        flex-direction: column;
        align-items: center;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.person-card {
    animation: fadeInUp 0.6s ease forwards;
}

.person-card:nth-child(even) {
    animation-delay: 0.1s;
}

.person-card:nth-child(odd) {
    animation-delay: 0.2s;
}

/* Element UI 自定义样式 */
.el-button--primary {
    background: linear-gradient(135deg, #409EFF, #67C23A);
    border: none;
}

.el-button--danger {
    background: linear-gradient(135deg, #F56C6C, #E6A23C);
    border: none;
}

.el-button--warning {
    background: linear-gradient(135deg, #E6A23C, #F56C6C);
    border: none;
}

.el-button--success {
    background: linear-gradient(135deg, #67C23A, #409EFF);
    border: none;
}

.el-input-number .el-input__inner {
    border-radius: 6px;
}
