<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支部委员会换届选举投票统计</title>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="index.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1 class="title">
                    <i class="el-icon-pie-chart"></i>
                    支部委员会换届选举投票统计
                </h1>
                <div class="stats">
                    <el-card class="stats-card total">
                        <div class="stats-item">
                            <span class="stats-label">总票数：</span>
                            <span class="stats-value">{{ totalVotes }}</span>
                        </div>
                    </el-card>
                    <el-card class="stats-card agree">
                        <div class="stats-item">
                            <span class="stats-label">同意票：</span>
                            <span class="stats-value">{{ totalAgreeVotes }}</span>
                        </div>
                    </el-card>
                    <el-card class="stats-card disagree">
                        <div class="stats-item">
                            <span class="stats-label">不同意票：</span>
                            <span class="stats-value">{{ totalDisagreeVotes }}</span>
                        </div>
                    </el-card>
                    <el-card class="stats-card abstain">
                        <div class="stats-item">
                            <span class="stats-label">弃权票：</span>
                            <span class="stats-value">{{ totalAbstainVotes }}</span>
                        </div>
                    </el-card>
                </div>
            </div>

            <!-- 候选人区域 -->
            <div class="section-header candidate-header">
                <h2 class="section-title">
                    <i class="el-icon-star-on"></i>
                    候选人
                </h2>
            </div>
            <div class="voting-grid candidate-grid">
                <el-card
                    v-for="(person, index) in candidates"
                    :key="'candidate-' + index"
                    class="person-card candidate-card"
                    shadow="hover"
                >
                    <div class="person-info">
                        <div class="person-avatar candidate-avatar">
                            <i class="el-icon-user-solid"></i>
                        </div>
                        <div class="person-details">
                            <h3 class="person-name">{{ person.name }}</h3>
                            <div class="vote-section">
                                <!-- 同意票 -->
                                <div class="vote-row">
                                    <div class="vote-display">
                                        <span class="vote-label agree-label">同意：</span>
                                        <span class="vote-count agree-count">{{ person.agree }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-button
                                            type="danger"
                                            icon="el-icon-minus"
                                            circle
                                            size="mini"
                                            @click="decreaseVote(index, 'candidate', 'agree')"
                                            :disabled="person.agree <= 0"
                                        ></el-button>
                                        <el-input-number
                                            v-model="person.agree"
                                            :min="0"
                                            :max="9999"
                                            size="mini"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                        <el-button
                                            type="success"
                                            icon="el-icon-plus"
                                            circle
                                            size="mini"
                                            @click="increaseVote(index, 'candidate', 'agree')"
                                        ></el-button>
                                    </div>
                                </div>

                                <!-- 不同意票 -->
                                <div class="vote-row">
                                    <div class="vote-display">
                                        <span class="vote-label disagree-label">不同意：</span>
                                        <span class="vote-count disagree-count">{{ person.disagree }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-button
                                            type="danger"
                                            icon="el-icon-minus"
                                            circle
                                            size="mini"
                                            @click="decreaseVote(index, 'candidate', 'disagree')"
                                            :disabled="person.disagree <= 0"
                                        ></el-button>
                                        <el-input-number
                                            v-model="person.disagree"
                                            :min="0"
                                            :max="9999"
                                            size="mini"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                        <el-button
                                            type="danger"
                                            icon="el-icon-plus"
                                            circle
                                            size="mini"
                                            @click="increaseVote(index, 'candidate', 'disagree')"
                                        ></el-button>
                                    </div>
                                </div>

                                <!-- 弃权票 -->
                                <div class="vote-row">
                                    <div class="vote-display">
                                        <span class="vote-label abstain-label">弃权：</span>
                                        <span class="vote-count abstain-count">{{ person.abstain }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-button
                                            type="danger"
                                            icon="el-icon-minus"
                                            circle
                                            size="mini"
                                            @click="decreaseVote(index, 'candidate', 'abstain')"
                                            :disabled="person.abstain <= 0"
                                        ></el-button>
                                        <el-input-number
                                            v-model="person.abstain"
                                            :min="0"
                                            :max="9999"
                                            size="mini"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                        <el-button
                                            type="warning"
                                            icon="el-icon-plus"
                                            circle
                                            size="mini"
                                            @click="increaseVote(index, 'candidate', 'abstain')"
                                        ></el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>

            <!-- 备用人员区域 -->
            <div class="section-header backup-header">
                <h2 class="section-title">
                    <i class="el-icon-user"></i>
                    备选人
                </h2>
            </div>
            <div class="voting-grid backup-grid">
                <el-card
                    v-for="(person, index) in backupPersonnel"
                    :key="'backup-' + index"
                    class="person-card backup-card"
                    shadow="hover"
                >
                    <div class="person-info">
                        <div class="person-avatar backup-avatar">
                            <i class="el-icon-user-solid"></i>
                        </div>
                        <div class="person-details">
                            <h3 class="person-name">{{ person.name }}</h3>
                            <div class="vote-section">
                                <!-- 同意票 -->
                                <div class="vote-row">
                                    <div class="vote-display">
                                        <span class="vote-label agree-label">同意：</span>
                                        <span class="vote-count agree-count">{{ person.agree }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-button
                                            type="danger"
                                            icon="el-icon-minus"
                                            circle
                                            size="mini"
                                            @click="decreaseVote(index, 'backup', 'agree')"
                                            :disabled="person.agree <= 0"
                                        ></el-button>
                                        <el-input-number
                                            v-model="person.agree"
                                            :min="0"
                                            :max="9999"
                                            size="mini"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                        <el-button
                                            type="success"
                                            icon="el-icon-plus"
                                            circle
                                            size="mini"
                                            @click="increaseVote(index, 'backup', 'agree')"
                                        ></el-button>
                                    </div>
                                </div>

                                <!-- 不同意票 -->
                                <div class="vote-row">
                                    <div class="vote-display">
                                        <span class="vote-label disagree-label">不同意：</span>
                                        <span class="vote-count disagree-count">{{ person.disagree }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-button
                                            type="danger"
                                            icon="el-icon-minus"
                                            circle
                                            size="mini"
                                            @click="decreaseVote(index, 'backup', 'disagree')"
                                            :disabled="person.disagree <= 0"
                                        ></el-button>
                                        <el-input-number
                                            v-model="person.disagree"
                                            :min="0"
                                            :max="9999"
                                            size="mini"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                        <el-button
                                            type="danger"
                                            icon="el-icon-plus"
                                            circle
                                            size="mini"
                                            @click="increaseVote(index, 'backup', 'disagree')"
                                        ></el-button>
                                    </div>
                                </div>

                                <!-- 弃权票 -->
                                <div class="vote-row">
                                    <div class="vote-display">
                                        <span class="vote-label abstain-label">弃权：</span>
                                        <span class="vote-count abstain-count">{{ person.abstain }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-button
                                            type="danger"
                                            icon="el-icon-minus"
                                            circle
                                            size="mini"
                                            @click="decreaseVote(index, 'backup', 'abstain')"
                                            :disabled="person.abstain <= 0"
                                        ></el-button>
                                        <el-input-number
                                            v-model="person.abstain"
                                            :min="0"
                                            :max="9999"
                                            size="mini"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                        <el-button
                                            type="warning"
                                            icon="el-icon-plus"
                                            circle
                                            size="mini"
                                            @click="increaseVote(index, 'backup', 'abstain')"
                                        ></el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>

            <div class="footer">
                <el-button type="warning" @click="resetAllVotes" icon="el-icon-refresh">
                    重置所有票数
                </el-button>
                <el-button type="success" @click="exportData" icon="el-icon-download">
                    导出数据
                </el-button>
            </div>
        </div>
    </div>

    <!-- Vue.js -->
    <script src="vue.js"></script>
    <!-- Element UI JS -->
    <script src="index.js"></script>
    <!-- 自定义脚本 -->
    <script src="script.js"></script>
</body>
</html>