<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投票统计系统</title>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1 class="title">
                    <i class="el-icon-pie-chart"></i>
                    投票统计系统
                </h1>
                <div class="stats">
                    <el-card class="stats-card total">
                        <div class="stats-item">
                            <span class="stats-label">总票数：</span>
                            <span class="stats-value">{{ totalVotes }}</span>
                        </div>
                    </el-card>
                    <el-card class="stats-card candidate">
                        <div class="stats-item">
                            <span class="stats-label">候选人票数：</span>
                            <span class="stats-value">{{ candidateVotes }}</span>
                        </div>
                    </el-card>
                    <el-card class="stats-card backup">
                        <div class="stats-item">
                            <span class="stats-label">备用人员票数：</span>
                            <span class="stats-value">{{ backupVotes }}</span>
                        </div>
                    </el-card>
                </div>
            </div>

            <!-- 候选人区域 -->
            <div class="section-header candidate-header">
                <h2 class="section-title">
                    <i class="el-icon-star-on"></i>
                    候选人名单
                </h2>
            </div>
            <div class="voting-grid candidate-grid">
                <el-card
                    v-for="(person, index) in candidates"
                    :key="'candidate-' + index"
                    class="person-card candidate-card"
                    shadow="hover"
                >
                    <div class="person-info">
                        <div class="person-avatar candidate-avatar">
                            <i class="el-icon-user-solid"></i>
                        </div>
                        <div class="person-details">
                            <h3 class="person-name">{{ person.name }}</h3>
                            <div class="vote-section">
                                <div class="vote-display">
                                    <span class="vote-label">得票数：</span>
                                    <span class="vote-count">{{ person.votes }}</span>
                                </div>
                                <div class="vote-controls">
                                    <el-button
                                        type="danger"
                                        icon="el-icon-minus"
                                        circle
                                        size="small"
                                        @click="decreaseVote(index, 'candidate')"
                                        :disabled="person.votes <= 0"
                                    ></el-button>
                                    <el-input-number
                                        v-model="person.votes"
                                        :min="0"
                                        :max="9999"
                                        size="small"
                                        @change="saveData"
                                        class="vote-input"
                                    ></el-input-number>
                                    <el-button
                                        type="primary"
                                        icon="el-icon-plus"
                                        circle
                                        size="small"
                                        @click="increaseVote(index, 'candidate')"
                                    ></el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>

            <!-- 备用人员区域 -->
            <div class="section-header backup-header">
                <h2 class="section-title">
                    <i class="el-icon-user"></i>
                    备用人员名单
                </h2>
            </div>
            <div class="voting-grid backup-grid">
                <el-card
                    v-for="(person, index) in backupPersonnel"
                    :key="'backup-' + index"
                    class="person-card backup-card"
                    shadow="hover"
                >
                    <div class="person-info">
                        <div class="person-avatar backup-avatar">
                            <i class="el-icon-user-solid"></i>
                        </div>
                        <div class="person-details">
                            <h3 class="person-name">{{ person.name }}</h3>
                            <div class="vote-section">
                                <div class="vote-display">
                                    <span class="vote-label">得票数：</span>
                                    <span class="vote-count">{{ person.votes }}</span>
                                </div>
                                <div class="vote-controls">
                                    <el-button
                                        type="danger"
                                        icon="el-icon-minus"
                                        circle
                                        size="small"
                                        @click="decreaseVote(index, 'backup')"
                                        :disabled="person.votes <= 0"
                                    ></el-button>
                                    <el-input-number
                                        v-model="person.votes"
                                        :min="0"
                                        :max="9999"
                                        size="small"
                                        @change="saveData"
                                        class="vote-input"
                                    ></el-input-number>
                                    <el-button
                                        type="primary"
                                        icon="el-icon-plus"
                                        circle
                                        size="small"
                                        @click="increaseVote(index, 'backup')"
                                    ></el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>

            <div class="footer">
                <el-button type="warning" @click="resetAllVotes" icon="el-icon-refresh">
                    重置所有票数
                </el-button>
                <el-button type="success" @click="exportData" icon="el-icon-download">
                    导出数据
                </el-button>
            </div>
        </div>
    </div>

    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- Element UI JS -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- 自定义脚本 -->
    <script src="script.js"></script>
</body>
</html>