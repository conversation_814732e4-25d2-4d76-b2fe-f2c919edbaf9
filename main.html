<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投票统计系统</title>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1 class="title">
                    <i class="el-icon-pie-chart"></i>
                    投票统计系统
                </h1>
                <div class="stats">
                    <el-card class="stats-card">
                        <div class="stats-item">
                            <span class="stats-label">总票数：</span>
                            <span class="stats-value">{{ totalVotes }}</span>
                        </div>
                    </el-card>
                </div>
            </div>

            <div class="voting-grid">
                <el-card
                    v-for="(person, index) in people"
                    :key="index"
                    class="person-card"
                    shadow="hover"
                >
                    <div class="person-info">
                        <div class="person-avatar">
                            <i class="el-icon-user-solid"></i>
                        </div>
                        <div class="person-details">
                            <h3 class="person-name">{{ person.name }}</h3>
                            <div class="vote-section">
                                <div class="vote-display">
                                    <span class="vote-label">得票数：</span>
                                    <span class="vote-count">{{ person.votes }}</span>
                                </div>
                                <div class="vote-controls">
                                    <el-button
                                        type="danger"
                                        icon="el-icon-minus"
                                        circle
                                        size="small"
                                        @click="decreaseVote(index)"
                                        :disabled="person.votes <= 0"
                                    ></el-button>
                                    <el-input-number
                                        v-model="person.votes"
                                        :min="0"
                                        :max="9999"
                                        size="small"
                                        @change="saveData"
                                        class="vote-input"
                                    ></el-input-number>
                                    <el-button
                                        type="primary"
                                        icon="el-icon-plus"
                                        circle
                                        size="small"
                                        @click="increaseVote(index)"
                                    ></el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>

            <div class="footer">
                <el-button type="warning" @click="resetAllVotes" icon="el-icon-refresh">
                    重置所有票数
                </el-button>
                <el-button type="success" @click="exportData" icon="el-icon-download">
                    导出数据
                </el-button>
            </div>
        </div>
    </div>

    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- Element UI JS -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- 自定义脚本 -->
    <script src="script.js"></script>
</body>
</html>