# 投票统计系统

## 功能特点

✅ **完整功能实现**
- 20个人员名单显示
- 实时票数增减控制
- 本地数据缓存（localStorage）
- 1920*1080分辨率适配
- Vue.js + Element UI框架
- 美观的用户界面

## 文件结构

```
├── main.html          # 主页面文件
├── style.css          # 样式文件
├── script.js          # JavaScript逻辑文件
├── bg.jpg            # 背景图片（需要您提供）
└── README.md         # 说明文档
```

## 使用说明

1. **启动系统**：直接在浏览器中打开 `main.html` 文件

2. **投票操作**：
   - 点击 ➕ 按钮增加票数
   - 点击 ➖ 按钮减少票数
   - 直接在数字框中输入票数
   - 所有操作都会自动保存到本地

3. **数据管理**：
   - 系统自动保存数据到浏览器本地存储
   - 页面刷新后数据不会丢失
   - 可以重置所有票数
   - 可以导出数据为JSON文件

## 背景图片设置

请将您的背景图片命名为 `bg.jpg` 并放在与 `main.html` 同一目录下。

推荐图片规格：
- 分辨率：1920x1080 或更高
- 格式：JPG/PNG
- 文件大小：建议不超过2MB

如果没有背景图片，系统会显示渐变背景色。

## 技术特性

- **响应式设计**：适配不同屏幕尺寸
- **数据持久化**：使用localStorage本地存储
- **实时统计**：动态计算总票数
- **用户体验**：流畅的动画效果和交互反馈
- **数据安全**：自动保存和错误处理

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 自定义配置

如需修改人员名单，请编辑 `script.js` 文件中的 `people` 数组。
